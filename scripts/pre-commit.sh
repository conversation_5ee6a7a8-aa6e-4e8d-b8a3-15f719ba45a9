#!/bin/bash
# Pre-commit script for Audio FlowMatching Lightning project
# This script provides options for running pre-commit hooks in two modes:
# 1. Light mode (default): Essential formatting and linting only
# 2. Strict mode: Comprehensive linting including docstrings, security, and more
#
# Usage:
#   # First time setup (required):
#   ./scripts/pre-commit.sh --install # Install hooks (run once)
#
#   # Daily usage:
#   ./scripts/pre-commit.sh           # Run light pre-commit
#   ./scripts/pre-commit.sh --strict  # Run strict pre-commit
#
#   # Maintenance:
#   ./scripts/pre-commit.sh --clean   # Clean environments (if switching modes or having issues)
#   ./scripts/pre-commit.sh --update  # Update hook versions
#   ./scripts/pre-commit.sh --help    # Show help
#
# Recommended workflow:
#   1. Install once: ./scripts/pre-commit.sh --install
#   2. Daily use: ./scripts/pre-commit.sh (or --strict for thorough checks)
#   3. Clean if needed: ./scripts/pre-commit.sh --clean (when switching between modes)
#
# Features:
#   - Light mode (default): Essential formatting/linting only
#   - Strict mode: Comprehensive linting including docstrings, security, spelling
#   - Setup commands: Install, clean, update hooks
#   - Color-coded output: Clear status messages
#   - Error handling: Proper exit codes and validation
#   - Help documentation: Built-in usage examples

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    echo -e "${1}${2}${NC}"
}

print_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --light, -l         Run light pre-commit (default)"
    echo "  --strict, -s        Run strict pre-commit"
    echo "  --install, -i       Install pre-commit hooks"
    echo "  --clean, -c         Clean pre-commit environments"
    echo "  --update, -u        Update pre-commit hooks"
    echo "  --help, -h          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                  # Run light pre-commit"
    echo "  $0 --strict         # Run strict pre-commit"
    echo "  $0 --install        # Install pre-commit hooks"
    echo "  $0 --clean          # Clean pre-commit environments"
    echo ""
    echo "Configuration files:"
    echo "  .pre-commit-config.yaml         # Light version (default)"
    echo "  .pre-commit-config-strict.yaml  # Strict version"
}

check_precommit_installed() {
    if ! command -v pre-commit &> /dev/null; then
        print_color $RED "Error: pre-commit is not installed."
        print_color $YELLOW "Please install it with: pip install pre-commit"
        exit 1
    fi
}

install_hooks() {
    print_color $BLUE "Installing pre-commit hooks..."

    # Install light hooks (default)
    print_color $YELLOW "Installing light pre-commit hooks..."
    pre-commit install

    print_color $GREEN "Pre-commit hooks installed successfully!"
    print_color $YELLOW "Note: Light config is set as default. Use --strict for comprehensive linting."
}

clean_environments() {
    print_color $BLUE "Cleaning pre-commit environments..."
    pre-commit clean
    print_color $GREEN "Pre-commit environments cleaned!"
}

update_hooks() {
    print_color $BLUE "Updating pre-commit hooks..."

    print_color $YELLOW "Updating light config..."
    pre-commit autoupdate

    print_color $YELLOW "Updating strict config..."
    pre-commit autoupdate --config .pre-commit-config-strict.yaml

    print_color $GREEN "Pre-commit hooks updated!"
}

run_light_precommit() {
    print_color $BLUE "Running light pre-commit (essential formatting and linting only)..."
    print_color $YELLOW "Using: .pre-commit-config.yaml"

    print_color $YELLOW "Hooks included:"
    echo "  ✓ File cleanup (trailing whitespace, end-of-file)"
    echo "  ✓ Black (code formatting)"
    echo "  ✓ isort (import sorting)"
    echo "  ✓ pyupgrade (syntax upgrades)"
    echo "  ✓ flake8 (basic linting)"
    echo "  ✓ prettier (YAML formatting)"
    echo "  ✓ nbstripout & nbQA (notebook handling)"
    echo ""

    if pre-commit run --all-files; then
        print_color $GREEN "✅ Light pre-commit completed successfully!"
    else
        print_color $RED "❌ Light pre-commit found issues. Please fix them and try again."
        exit 1
    fi
}

run_strict_precommit() {
    print_color $BLUE "Running strict pre-commit (comprehensive linting)..."
    print_color $YELLOW "Using: .pre-commit-config-strict.yaml"

    print_color $YELLOW "Additional hooks included:"
    echo "  ✓ All light hooks PLUS:"
    echo "  ✓ docformatter (docstring formatting)"
    echo "  ✓ interrogate (docstring coverage)"
    echo "  ✓ bandit (security linting)"
    echo "  ✓ shellcheck (shell script linting)"
    echo "  ✓ mdformat (markdown formatting)"
    echo "  ✓ codespell (spelling checker)"
    echo ""

    if pre-commit run --config .pre-commit-config-strict.yaml --all-files; then
        print_color $GREEN "✅ Strict pre-commit completed successfully!"
    else
        print_color $RED "❌ Strict pre-commit found issues. Please fix them and try again."
        exit 1
    fi
}

# Parse command line arguments
MODE="light"
ACTION="run"

while [[ $# -gt 0 ]]; do
    case $1 in
        --light|-l)
            MODE="light"
            shift
            ;;
        --strict|-s)
            MODE="strict"
            shift
            ;;
        --install|-i)
            ACTION="install"
            shift
            ;;
        --clean|-c)
            ACTION="clean"
            shift
            ;;
        --update|-u)
            ACTION="update"
            shift
            ;;
        --help|-h)
            print_help
            exit 0
            ;;
        *)
            print_color $RED "Unknown option: $1"
            print_help
            exit 1
            ;;
    esac
done

# Check if pre-commit is installed
check_precommit_installed

# Execute based on action
case $ACTION in
    install)
        install_hooks
        ;;
    clean)
        clean_environments
        ;;
    update)
        update_hooks
        ;;
    run)
        case $MODE in
            light)
                run_light_precommit
                ;;
            strict)
                run_strict_precommit
                ;;
        esac
        ;;
esac
