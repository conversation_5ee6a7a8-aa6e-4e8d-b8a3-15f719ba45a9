#!/bin/bash
# Pre-commit script for Audio FlowMatching Lightning project
# This script provides options for running pre-commit hooks in two modes:
# 1. Light mode (default): Essential formatting and linting only
# 2. Strict mode: Comprehensive linting including docstrings, security, and more
#
# Usage:
#   # First time setup (required):
#   ./scripts/pre-commit.sh --install # Install hooks (run once)
#
#   # Daily usage:
#   ./scripts/pre-commit.sh           # Run light pre-commit
#   ./scripts/pre-commit.sh --strict  # Run strict pre-commit
#
#   # Maintenance:
#   ./scripts/pre-commit.sh --clean   # Clean environments (if switching modes or having issues)
#   ./scripts/pre-commit.sh --update  # Update hook versions
#   ./scripts/pre-commit.sh --help    # Show help
#
# Recommended workflow:
#   1. Install once: ./scripts/pre-commit.sh --install
#   2. Daily use: ./scripts/pre-commit.sh (or --strict for thorough checks)
#   3. Clean if needed: ./scripts/pre-commit.sh --clean (when switching between modes)
#
# Features:
#   - Light mode (default): Essential formatting/linting only
#   - Strict mode: Comprehensive linting including docstrings, security, spelling
#   - Setup commands: Install, clean, update hooks
#   - Color-coded output: Clear status messages
#   - Error handling: Proper exit codes and validation
#   - Help documentation: Built-in usage examples

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    echo -e "${1}${2}${NC}"
}

print_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --light, -l         Run light pre-commit (default)"
    echo "  --strict, -s        Run strict pre-commit"
    echo "  --install, -i       Install pre-commit hooks"
    echo "  --clean, -c         Clean pre-commit environments"
    echo "  --update, -u        Update pre-commit hooks"
    echo "  --status            Show pre-commit status and configuration"
    echo "  --help, -h          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                  # Run light pre-commit"
    echo "  $0 --strict         # Run strict pre-commit"
    echo "  $0 --install        # Install pre-commit hooks"
    echo "  $0 --clean          # Clean pre-commit environments"
    echo ""
    echo "Configuration files:"
    echo "  .pre-commit-config.yaml         # Light version (default)"
    echo "  .pre-commit-config-strict.yaml  # Strict version"
}

check_precommit_installed() {
    if ! command -v pre-commit &> /dev/null; then
        print_color $RED "Error: pre-commit is not installed."
        print_color $YELLOW "Please install it with: pip install pre-commit"
        exit 1
    fi
}

check_python_version() {
    local python_version
    python_version=$(python --version 2>&1 | cut -d' ' -f2)
    local major_minor
    major_minor=$(echo "$python_version" | cut -d'.' -f1,2)

    if [[ "$major_minor" < "3.13" ]]; then
        print_color $RED "Warning: Python version $python_version detected."
        print_color $YELLOW "This project requires Python 3.13+. Some hooks may not work correctly."
        print_color $YELLOW "Consider upgrading Python or using a virtual environment with Python 3.13+."
    else
        print_color $GREEN "Python version $python_version detected - compatible with project requirements."
    fi
}

check_config_files() {
    if [[ ! -f ".pre-commit-config.yaml" ]]; then
        print_color $RED "Error: .pre-commit-config.yaml not found."
        exit 1
    fi

    if [[ ! -f ".pre-commit-config-strict.yaml" ]]; then
        print_color $RED "Error: .pre-commit-config-strict.yaml not found."
        exit 1
    fi
}

install_hooks() {
    print_color $BLUE "Installing pre-commit hooks..."

    # Check config files exist
    check_config_files

    # Check Python version
    check_python_version
    echo ""

    # Install light hooks (default)
    print_color $YELLOW "Installing light pre-commit hooks..."
    if pre-commit install; then
        print_color $GREEN "Pre-commit hooks installed successfully!"
        print_color $YELLOW "Note: Light config is set as default. Use --strict for comprehensive linting."

        # Also install pre-push hooks for additional safety
        print_color $YELLOW "Installing pre-push hooks..."
        pre-commit install --hook-type pre-push

        print_color $GREEN "All hooks installed successfully!"
    else
        print_color $RED "Failed to install pre-commit hooks."
        exit 1
    fi
}

clean_environments() {
    print_color $BLUE "Cleaning pre-commit environments..."
    pre-commit clean
    print_color $GREEN "Pre-commit environments cleaned!"
}

update_hooks() {
    print_color $BLUE "Updating pre-commit hooks..."

    print_color $YELLOW "Updating light config..."
    pre-commit autoupdate

    print_color $YELLOW "Updating strict config..."
    pre-commit autoupdate --config .pre-commit-config-strict.yaml

    print_color $GREEN "Pre-commit hooks updated!"
}

show_status() {
    print_color $BLUE "Pre-commit Status and Configuration"
    echo "=================================="

    # Check if pre-commit is installed
    if command -v pre-commit &> /dev/null; then
        print_color $GREEN "✓ pre-commit is installed"
        echo "  Version: $(pre-commit --version)"
    else
        print_color $RED "✗ pre-commit is not installed"
        return 1
    fi

    # Check Python version
    check_python_version
    echo ""

    # Check if hooks are installed
    if pre-commit run --help &> /dev/null && [[ -d ".git/hooks" ]] && [[ -f ".git/hooks/pre-commit" ]]; then
        print_color $GREEN "✓ Pre-commit hooks are installed"
    else
        print_color $YELLOW "⚠ Pre-commit hooks are not installed"
        print_color $YELLOW "  Run: $0 --install"
    fi

    # Check config files
    echo ""
    print_color $BLUE "Configuration Files:"
    if [[ -f ".pre-commit-config.yaml" ]]; then
        print_color $GREEN "✓ .pre-commit-config.yaml (light mode)"
    else
        print_color $RED "✗ .pre-commit-config.yaml missing"
    fi

    if [[ -f ".pre-commit-config-strict.yaml" ]]; then
        print_color $GREEN "✓ .pre-commit-config-strict.yaml (strict mode)"
    else
        print_color $RED "✗ .pre-commit-config-strict.yaml missing"
    fi

    # Show recent pre-commit runs
    echo ""
    print_color $BLUE "Recent Activity:"
    if [[ -d ".git" ]]; then
        local last_commit
        last_commit=$(git log -1 --format="%h %s" 2>/dev/null || echo "No commits found")
        echo "  Last commit: $last_commit"
    fi

    echo ""
    print_color $YELLOW "Usage:"
    echo "  Light mode:  $0"
    echo "  Strict mode: $0 --strict"
}

run_light_precommit() {
    print_color $BLUE "Running light pre-commit (essential formatting and linting only)..."
    print_color $YELLOW "Using: .pre-commit-config.yaml"

    # Check config files and Python version
    check_config_files
    check_python_version
    echo ""

    print_color $YELLOW "Hooks included:"
    echo "  ✓ File cleanup (trailing whitespace, end-of-file)"
    echo "  ✓ Black (code formatting)"
    echo "  ✓ isort (import sorting)"
    echo "  ✓ pyupgrade (syntax upgrades to Python 3.13+)"
    echo "  ✓ flake8 (basic linting)"
    echo "  ✓ prettier (YAML formatting)"
    echo "  ✓ nbstripout & nbQA (notebook handling)"
    echo ""

    print_color $YELLOW "Running pre-commit checks..."
    if pre-commit run --all-files; then
        print_color $GREEN "✅ Light pre-commit completed successfully!"
        print_color $BLUE "All code formatting and basic linting checks passed."
    else
        print_color $RED "❌ Light pre-commit found issues. Please fix them and try again."
        print_color $YELLOW "Tip: Many formatting issues can be auto-fixed by running the hooks again."
        exit 1
    fi
}

run_strict_precommit() {
    print_color $BLUE "Running strict pre-commit (comprehensive linting)..."
    print_color $YELLOW "Using: .pre-commit-config-strict.yaml"

    # Check config files and Python version
    check_config_files
    check_python_version
    echo ""

    print_color $YELLOW "Additional hooks included:"
    echo "  ✓ All light hooks PLUS:"
    echo "  ✓ docformatter (docstring formatting)"
    echo "  ✓ interrogate (docstring coverage ≥60%)"
    echo "  ✓ bandit (security linting)"
    echo "  ✓ shellcheck (shell script linting)"
    echo "  ✓ mdformat (markdown formatting)"
    echo "  ✓ codespell (spelling checker)"
    echo ""

    print_color $YELLOW "Running comprehensive pre-commit checks..."
    print_color $BLUE "Note: This may take longer than light mode..."
    if pre-commit run --config .pre-commit-config-strict.yaml --all-files; then
        print_color $GREEN "✅ Strict pre-commit completed successfully!"
        print_color $BLUE "All comprehensive linting and security checks passed."
    else
        print_color $RED "❌ Strict pre-commit found issues. Please fix them and try again."
        print_color $YELLOW "Tip: Consider running light mode first to fix basic formatting issues."
        exit 1
    fi
}

# Parse command line arguments
MODE="light"
ACTION="run"

while [[ $# -gt 0 ]]; do
    case $1 in
        --light|-l)
            MODE="light"
            shift
            ;;
        --strict|-s)
            MODE="strict"
            shift
            ;;
        --install|-i)
            ACTION="install"
            shift
            ;;
        --clean|-c)
            ACTION="clean"
            shift
            ;;
        --update|-u)
            ACTION="update"
            shift
            ;;
        --status)
            ACTION="status"
            shift
            ;;
        --help|-h)
            print_help
            exit 0
            ;;
        *)
            print_color $RED "Unknown option: $1"
            print_help
            exit 1
            ;;
    esac
done

# Check if pre-commit is installed
check_precommit_installed

# Execute based on action
case $ACTION in
    install)
        install_hooks
        ;;
    clean)
        clean_environments
        ;;
    update)
        update_hooks
        ;;
    status)
        show_status
        ;;
    run)
        case $MODE in
            light)
                run_light_precommit
                ;;
            strict)
                run_strict_precommit
                ;;
        esac
        ;;
esac
