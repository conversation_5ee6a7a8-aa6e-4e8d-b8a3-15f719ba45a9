## Pytorch
torch>=2.7
torchaudio>=2.7.0
torchvision>=0.22

## Lightning
lightning>=2.5.1
torchmetrics>=1.7.1

## Hydra
hydra-core>=1.3.2
hydra-colorlog>=1.2.0
hydra-optuna-sweeper>=1.2.0

## loggers
wandb
tensorboard
# neptune-client
# mlflow
# comet-ml
# aim>=3.16.2  # no lower than 3.16.2, see https://github.com/aimhubio/aim/issues/2550

## others
rootutils       # standardizing the project root setup
pre-commit      # hooks for applying linters on commit
rich            # beautiful text formatting in terminal
# pytest          # tests
# sh            # for running bash commands in some tests (linux/macos only)

## Debugging and Linting
flake8
mypy
pudb
ipdb
docformatter

# Additional dependencies
# numpy==1.21.2
# pandas==1.3.3
# scikit-learn==0.24.2

matplotlib==3.10.3
librosa==0.11.0
torchdiffeq==0.2.5
einops==0.8.1
torchcfm==1.0.7
bigvgan==2.4.1
git+https://github.com/descriptinc/descript-audio-codec
