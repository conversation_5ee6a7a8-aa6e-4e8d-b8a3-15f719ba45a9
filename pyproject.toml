[build-system]
requires = ["setuptools", "setuptools-scm", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "Audio_FlowMatching_Lightning"
dynamic = ["version", "readme"]
description = "Audio Flow Matching Lightning Recipe."
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
license = { text="MIT" }
requires-python = ">=3.13"
keywords = ["Audio", "Speech"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Natural Language :: English",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "torch>=2.7",
    "torchaudio>=2.7.0",
    "torchvision>=0.22",
    "lightning>=2.5.1",
    "torchmetrics>=1.7.1",
    "hydra-core>=1.3.2",
    "hydra-colorlog>=1.2.0",
    "hydra-optuna-sweeper>=1.2.0",
    "wandb",
    "tensorboard",
    "rootutils",
    "pre-commit",
    "rich",
    "matplotlib==3.10.3",
    "librosa==0.11.0",
    "torchdiffeq==0.2.5",
    "einops==0.8.1",
    "torchcfm==1.0.7",
    "bigvgan==2.4.1",
    "descript-audio-codec @ git+https://github.com/descriptinc/descript-audio-codec",
    "audidata==0.0.5"
]

[project.optional-dependencies]
dev = [
    "flake8",
    "mypy",
    "pudb",
    "ipdb"
]
# test = [
#     "pytest",
#     "pytest-cov"
# ]
# docs = [
#     "sphinx",
#     "sphinx-rtd-theme"
# ]

# [tool.pip]
# extra-index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"

[tool.setuptools]
packages = { find = { where = ["src"], exclude = ["tests", "docs", "data", "logs", "scripts"] } }

[tool.setuptools.dynamic]
version = {attr = "reproduceNanoGPT.__version__"}
readme = {file = ["README.md", "README.rst"]}

[project.scripts]
train_command = "src.train:main"
eval_command = "src.eval:main"

# [project.urls]
# Homepage = "https://example.com"
# Documentation = "https://docs.example.com"
# Source = "https://github.com/username/example_project"
# Tracker = "https://github.com/username/example_project/issues"
