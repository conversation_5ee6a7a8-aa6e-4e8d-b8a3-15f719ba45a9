default_language_version:
  python: python3.13

# Performance optimizations
fail_fast: false
default_install_hook_types: [pre-commit, pre-merge-commit, pre-push]

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      # list of supported hooks: https://pre-commit.com/hooks.html
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-docstring-first
      - id: check-yaml
      - id: debug-statements
      - id: detect-private-key
      - id: check-executables-have-shebangs
        exclude: ^pyproject\.toml$
      - id: check-toml
      - id: check-case-conflict
      - id: check-added-large-files

  # python code formatting
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        args: [--line-length, "99"]

  # python import sorting
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]

  # python upgrading syntax to newer version
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.0
    hooks:
      - id: pyupgrade
        args: [--py313-plus]

  # python docstring formatting (using local installation)
  - repo: local
    hooks:
      - id: docformatter
        name: docformatter
        entry: python -m docformatter
        language: system
        args:
          - "--in-place"
          - "--wrap-summaries=99"
          - "--wrap-descriptions=99"
        types: [python]

  # python docstring coverage checking
  - repo: https://github.com/econchick/interrogate
    rev: 1.7.0 # or master if you're bold
    hooks:
      - id: interrogate
        args:
          [
            --verbose,
            --fail-under=60,
            --ignore-init-module,
            --ignore-init-method,
            --ignore-module,
            --ignore-nested-functions,
            -vv,
          ]

  # python check (PEP8), programming errors and code complexity
  - repo: https://github.com/PyCQA/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
        args:
          [
            "--extend-ignore",
            "E203,E402,E501,F401,F841,RST2,RST301",
            "--exclude",
            "logs/*,data/*,outputs/*,*.log",
          ]
        additional_dependencies: [flake8-rst-docstrings==0.3.0]

  # python security linter
  - repo: https://github.com/PyCQA/bandit
    rev: "1.8.0"
    hooks:
      - id: bandit
        args: ["-s", "B101,B301,B403,B404,B311"]

  # yaml formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types: [yaml]
        exclude: "environment.yaml|.pre-commit-config*.yaml"

  # shell scripts linter
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.10.0.1
    hooks:
      - id: shellcheck

  # md formatting
  - repo: https://github.com/hukkin/mdformat
    rev: 0.7.19
    hooks:
      - id: mdformat
        args: ["--number"]
        additional_dependencies:
          - mdformat-gfm
          - mdformat-tables
          - mdformat_frontmatter
          # - mdformat-toc
          # - mdformat-black

  # word spelling linter
  - repo: https://github.com/codespell-project/codespell
    rev: v2.3.0
    hooks:
      - id: codespell
        args:
          - --skip=logs/**,data/**,*.ipynb,outputs/**
          - --ignore-words-list=nd,te,ba,ND,TE,BA
          # - --ignore-words-list=abc,def

  # jupyter notebook cell output clearing
  - repo: https://github.com/kynan/nbstripout
    rev: 0.7.1
    hooks:
      - id: nbstripout

  # jupyter notebook linting
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.9.1
    hooks:
      - id: nbqa-black
        args: ["--line-length=99"]
      - id: nbqa-isort
        args: ["--profile=black"]
      - id: nbqa-flake8
        args:
          [
            "--extend-ignore=E203,E402,E501,F401,F841",
            "--exclude=logs/*,data/*,outputs/*,*.log",
          ]
