#!/bin/bash

# Set environment variables
export HF_ENDPOINT="https://hf-mirror.com"
export HF_HOME="/home/<USER>/Workspace/huggingface"
export HF_HUB_CACHE="/home/<USER>/Workspace/huggingface/hub"
export TOKENIZERS_PARALLELISM="false"
export TORCH_HOME="/home/<USER>/Workspace/torch"
export WANDB_API_KEY="****************************************"

# Print environment variables to verify
echo "HF_ENDPOINT=$HF_ENDPOINT"
echo "HF_HOME=$HF_HOME"
echo "HF_HUB_CACHE=$HF_HUB_CACHE"
echo "TOKENIZERS_PARALLELISM=$TOKENIZERS_PARALLELISM"
echo "TORCH_HOME=$TORCH_HOME"
echo "WANDB_API_KEY=$WANDB_API_KEY"
