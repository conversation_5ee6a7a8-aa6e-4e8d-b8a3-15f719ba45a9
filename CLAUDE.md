# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Audio Flow Matching Lightning Recipe project that implements audio generation using flow matching techniques with PyTorch Lightning. The project focuses on audio processing, flow matching models, and provides training/evaluation infrastructure for audio generation tasks.

## Key Dependencies

- **PyTorch Lightning**: Core framework for model training and evaluation
- **Hydra**: Configuration management system
- **torchcfm**: Flow matching implementation
- **bigvgan**: Neural vocoder for audio generation
- **descript-audio-codec**: Audio codec for compression
- **librosa**: Audio processing utilities
- **wandb/tensorboard**: Experiment tracking

## Common Commands

### Development Commands
```bash
# Install dependencies
pip install -e .

# Run pre-commit hooks (formatting, linting, security checks)
make format
# or
pre-commit run -a

# Clean up generated files
make clean

# Clean logs
make clean-logs
```

### Training and Evaluation
```bash
# Train model with default configuration
make train
# or
python src/train.py

# Train with specific configuration
python src/train.py data=gtzan trainer=gpu

# Evaluate model
python src/eval.py ckpt_path=path/to/checkpoint.ckpt

# Train with custom parameters
python src/train.py trainer.max_epochs=100 data.batch_size=64
```

### Testing
```bash
# Run tests (excluding slow tests)
make test
# or
pytest -k "not slow"

# Run all tests
make test-full
# or
pytest
```

### Code Quality
```bash
# Linting with flake8
flake8 src/

# Type checking with mypy
mypy src/

# Code formatting
black src/ --line-length=99
isort src/ --profile=black
```

## Architecture Overview

### Configuration System
- **Hydra-based configuration**: All configurations are in `configs/` directory
- **Modular configs**: Separate configs for data, models, trainer, callbacks, etc.
- **Environment-specific**: Different trainer configs for CPU, GPU, DDP
- **Experiment tracking**: Built-in support for wandb, tensorboard, and other loggers

### Core Components

#### Data Pipeline (`src/data/`)
- **DataModules**: Lightning data modules for different datasets (GTZAN, MNIST)
- **Transforms**: Audio preprocessing (ToMono, OneHot encoding, AudioCrop)
- **Components**: Dataset implementations with audio loading and processing

#### Models (`src/models/`)
- **Modular architecture**: Separates model components for reusability
- **Lightning modules**: Training/validation/test logic in `src/lit_module/`
- **Flow matching**: Integration with torchcfm for flow-based generation

#### Training Infrastructure (`src/`)
- **train.py**: Main training script with Hydra configuration
- **eval.py**: Evaluation script for model checkpoints
- **utils/**: Utility functions for instantiation, logging, metrics

#### Configuration Structure
- **Hierarchical configs**: Default values with environment-specific overrides
- **Instantiation system**: Automatic instantiation of models, losses, metrics via Hydra
- **Experiment management**: Tag-based experiment organization

### Key Patterns

1. **Hydra Integration**: All components are instantiated via Hydra's `_target_` mechanism
2. **Lightning Best Practices**: Proper separation of model logic and training loops
3. **Modular Design**: Components are independently configurable and swappable
4. **Reproducibility**: Seed management and deterministic training support

## Development Workflow

1. **Configuration**: Modify configs in `configs/` directory or override via command line
2. **Data**: Add new datasets in `src/data/components/` and create corresponding datamodules
3. **Models**: Implement new models in `src/models/` and create Lightning modules in `src/lit_module/`
4. **Training**: Use `python src/train.py` with appropriate config overrides
5. **Evaluation**: Use `python src/eval.py` with checkpoint path for model evaluation

## Important Notes

- **Audio Focus**: Project is specifically designed for audio generation and processing
- **Flow Matching**: Uses torchcfm for flow-based generative modeling
- **Multi-GPU Support**: Configured for distributed training with DDP
- **Experiment Tracking**: Built-in logging to wandb, tensorboard, and other platforms
- **Code Quality**: Enforced via pre-commit hooks including black, isort, flake8, and security checks