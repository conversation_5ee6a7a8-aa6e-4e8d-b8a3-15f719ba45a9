"""GTZAN PyTorch Dataset.

GTZAN [1] is a music dataset containing 1,000 30-second audio clips.
The total duration is 8.3 hours. GTZAN includes 10 genres. All audio files
are mono sampled at 22,050 Hz. After decompression, the dataset size is
1.3 GB.

[1] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al., Musical genre classification of audio signals. 2002

The dataset looks like:

    gtzan (1.3 GB)
    └── genres
        ├── blues (100 files)
        ├── classical (100 files)
        ├── country (100 files)
        ├── disco (100 files)
        ├── hiphop (100 files)
        ├── jazz (100 files)
        ├── metal (100 files)
        ├── pop (100 files)
        ├── reggae (100 files)
        └── rock (100 files)
"""

import os
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple

from torch.utils.data import Dataset

try:
    import torchaudio
except ImportError as e:
    raise ImportError("torchaudio is required for GTZAN dataset") from e

from src.data.components.io.crop import AudioCrop
from src.data.components.io.load import load_audio
from src.data.components.transforms.common import ToMono
from src.data.components.transforms.onehot import OneHot


class GTZAN(Dataset):
    r"""GTZAN music genre dataset for PyTorch."""

    LABELS = [
        "blues",
        "classical",
        "country",
        "disco",
        "hiphop",
        "jazz",
        "metal",
        "pop",
        "reggae",
        "rock",
    ]
    CLASSES_NUM = len(LABELS)
    LB_TO_IX = {lb: ix for ix, lb in enumerate(LABELS)}
    IX_TO_LB = {ix: lb for ix, lb in enumerate(LABELS)}

    def __init__(
        self,
        data_dir: str,
        split: str = "train",
        test_fold: int = 0,
        sr: int = 22050,
        crop: Optional[Callable] = None,
        clip_duration: Optional[float] = 5.0,
        transform: Optional[Callable] = None,
        target_transform: Optional[Callable] = None,
    ):
        """
        Args:
            data_dir: data_dir directory of GTZAN dataset.
            split: "train" or "test".
            test_fold: Which fold to use for testing (0-9).
            sr: Audio sampling rate.
            crop: Callable for cropping audio (returns start, duration).
            clip_duration: Duration of audio clips in seconds.
            transform: Callable applied to audio after loading.
            target_transform: Callable applied to label index.
        """
        super().__init__()
        self.data_dir = data_dir
        self.split = split
        self.test_fold = test_fold
        self.sr = sr
        self.clip_duration = clip_duration

        # Set up crop based on split - different behavior for train/test
        if self.split == "train":
            self.crop = (
                crop
                if crop is not None
                else AudioCrop(clip_duration=self.clip_duration, random_crop=True)
            )
        elif self.split == "test":
            self.crop = (
                crop
                if crop is not None
                else AudioCrop(clip_duration=self.clip_duration, random_crop=False)
            )
        else:
            raise ValueError(f"Invalid split: {split}. Must be 'train' or 'test'.")

        # Set up transforms with defaults for standalone usage
        self.transform = transform if transform is not None else ToMono()
        self.target_transform = (
            target_transform
            if target_transform is not None
            else OneHot(num_classes=self.CLASSES_NUM)
        )

        self.labels = self.LABELS
        self.lb_to_ix = self.LB_TO_IX
        self.ix_to_lb = self.IX_TO_LB

        genres_dir = Path(self.data_dir, "genres")
        if not genres_dir.exists():
            raise RuntimeError(f"GTZAN directory not found at {genres_dir}")

        self.meta_dict = self._load_meta()

    def __len__(self) -> int:
        return len(self.meta_dict["audio_name"])

    def __getitem__(self, idx: int) -> dict[str, Any]:
        audio_path = self.meta_dict["audio_path"][idx]
        label = self.meta_dict["label"][idx]
        data = {"dataset_name": "GTZAN", "audio_path": audio_path}

        # Load audio
        audio_data = self._load_audio_data(audio_path)
        data.update(audio_data)

        # Load target/label
        target_data = self._load_target_data(label)
        data.update(target_data)

        return data

    def _load_meta(self) -> dict[str, list]:
        """Build a metadata dictionary for all audio files in the split."""
        meta = {"audio_name": [], "audio_path": [], "label": []}
        genres_dir = Path(self.data_dir, "genres")

        for genre in self.labels:
            genre_dir = genres_dir / genre
            audio_names = sorted(os.listdir(genre_dir))
            train_names, test_names = self._split_train_test(audio_names)
            selected = train_names if self.split == "train" else test_names

            for audio_name in selected:
                meta["audio_name"].append(audio_name)
                meta["audio_path"].append(str(genre_dir / audio_name))
                meta["label"].append(genre)

        return meta

    def _split_train_test(self, audio_names: list[str]) -> tuple[list[str], list[str]]:
        """Split audio names into train/test folds using file ID extraction."""
        train, test = [], []
        test_ids = set(range(self.test_fold * 10, (self.test_fold + 1) * 10))

        for name in audio_names:
            audio_id = self._extract_file_id(name)
            if audio_id in test_ids:
                test.append(name)
            else:
                train.append(name)

        return train, test

    def _extract_file_id(self, filename: str) -> int:
        """Extract file ID from GTZAN filename format (e.g., 'blues.00001.wav' -> 1)."""
        try:
            # GTZAN files follow pattern: genre.XXXXX.wav
            parts = filename.split(".")
            if len(parts) >= 2:
                # Extract the numeric part (e.g., '00001')
                numeric_part = parts[1]
                return int(numeric_part)
            else:
                # Fallback: extract any digits from filename
                import re

                match = re.search(r"(\d+)", filename)
                return int(match.group(1)) if match else 0
        except (ValueError, IndexError):
            # If extraction fails, return 0 (will be assigned to train)
            return 0

    def _load_audio_data(self, path: str) -> dict[str, Any]:
        """Load and process audio with error handling."""
        try:
            info = torchaudio.info(path)
            audio_duration = info.num_frames / info.sample_rate
        except (RuntimeError, OSError) as e:
            raise RuntimeError(f"Failed to get audio info from {path}: {e}")

        # Apply cropping
        start_time, clip_duration = self.crop(audio_duration=audio_duration)

        # Load audio with unified approach
        audio = self._load_audio_unified(path, start_time, clip_duration)

        # Ensure audio is 2D (channels, samples)
        if audio.ndim == 1:
            audio = audio.unsqueeze(0)

        # Apply transform
        if self.transform:
            audio = self.transform(audio)

        return {
            "audio": audio,
            "start_time": start_time,
            "duration": clip_duration,
        }

    def _load_audio_unified(self, path: str, start_time: float, clip_duration: float):
        """Unified audio loading with fallback mechanism."""
        try:
            # First try: use custom load_audio function
            audio, _ = load_audio(
                path=path,
                sr=self.sr,
                offset=start_time,
                duration=clip_duration,
            )
            return audio
        except (RuntimeError, OSError, ImportError) as e:
            # Fallback: use torchaudio directly
            try:
                frame_offset = int(start_time * self.sr)
                num_frames = int(clip_duration * self.sr)
                audio, sample_rate = torchaudio.load(
                    path, frame_offset=frame_offset, num_frames=num_frames
                )

                # Resample if necessary
                if sample_rate != self.sr:
                    resampler = torchaudio.transforms.Resample(
                        orig_freq=sample_rate, new_freq=self.sr
                    )
                    audio = resampler(audio)

                return audio
            except (RuntimeError, OSError) as fallback_e:
                raise RuntimeError(
                    f"Failed to load audio from {path}. "
                    f"Primary error: {e}. Fallback error: {fallback_e}"
                )

    def _load_target_data(self, label: str) -> dict[str, Any]:
        """Convert label string to index and apply target transform."""
        target = self.lb_to_ix[label]

        # Apply target transform
        if self.target_transform:
            target = self.target_transform(target)

        return {"label": label, "target": target}


def _quick_test(data_dir: str):
    """Minimal test for GTZAN PyTorch Dataset."""
    print(f"Testing GTZAN on data_dir: {data_dir}")
    ds = GTZAN(data_dir=data_dir)
    x = ds[0]
    print(f"Keys: {list(x.keys())}")
    print(f"Audio shape: {x['audio'].shape}")
    print(f"Label: {x['label']}, Target: {x['target']}")
    print("Test passed!")


if __name__ == "__main__":
    # _GTZAN_ROOT = "/path/to/your/gtzan"
    _GTZAN_ROOT = "/home/<USER>/Workspace/datasets/gtzan"
    if not os.path.exists(_GTZAN_ROOT):
        print("Please set _GTZAN_ROOT to a valid GTZAN data_dir directory.")
    else:
        _quick_test(_GTZAN_ROOT)
