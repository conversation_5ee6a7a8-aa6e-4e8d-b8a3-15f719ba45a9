"""OneHot Label Encoder for PyTorch.

This module provides a simple class for converting categorical class indices into one-hot encoded
torch tensors. Designed for use in PyTorch-based deep learning workflows, this utility is useful
for preparing classification targets for loss functions such as nn.CrossEntropyLoss, nn.BCELoss,
etc.
"""

import torch


class OneHot:
    """OneHot label encoder for PyTorch.

    This transform converts a class index into a one-hot encoded tensor of shape (num_classes,).
    Useful for training classifiers with categorical targets.

    Args:
        num_classes (int): Total number of possible classes.
    """

    def __init__(self, num_classes: int):
        self.num_classes = num_classes

    def __call__(self, index: int) -> torch.Tensor:
        """Convert a class index to a one-hot encoded tensor.

        Args:
            index (int): The class index to encode (0-based).

        Returns:
            torch.Tensor: A one-hot tensor of shape (num_classes,) with dtype torch.float32.
        """
        target = torch.zeros(self.num_classes, dtype=torch.float32)
        target[index] = 1.0
        return target
