# @package _global_

defaults:
  - _self_
  - data: mnist # choose datamodule with `test_dataloader()` for evaluation
  - models: simple_dense_net # choose model to evaluate
  - losses: default # choose loss function, not used in evaluation
  - metrics: default # choose metrics to evaluate
  - lit_module: mnist # choose lit module to evaluate
  - loggers: tensorboard # set logger here or use command line (e.g. `python eval.py logger=tensorboard`)
  - trainer: gpu
  - paths: default
  - extras: default
  - hydra: default

# task name, determines output directory path
task_name: "eval"

# tags to help you identify your experiments
# you can overwrite this in experiment configs
# overwrite from command line with `python eval.py tags="[first_tag, second_tag]"`
tags: ["dev"]

# passing checkpoint path is necessary for evaluation
ckpt_path: "/home/<USER>/Workspace/codes/Frameworks/simple-lightning-hydra-template/logs/train/runs/2025-06-04_15-48-31/checkpoints/last.ckpt"
