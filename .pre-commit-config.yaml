# Lighter pre-commit configuration for development
# For stricter linting, use: pre-commit run --config .pre-commit-config-strict.yaml --all-files

default_language_version:
  python: python3.13

# Performance optimizations
fail_fast: false
default_install_hook_types: [pre-commit, pre-merge-commit, pre-push]

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      # Essential file cleanup
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: check-case-conflict
      - id: check-added-large-files
      - id: debug-statements

  # python code formatting (essential)
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        args: [--line-length, "99"]

  # python import sorting (essential)
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]

  # python upgrading syntax to newer version
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.0
    hooks:
      - id: pyupgrade
        args: [--py313-plus]

  # python check (PEP8), programming errors and code complexity
  - repo: https://github.com/PyCQA/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
        args:
          [
            "--extend-ignore",
            "E203,E402,E501,F401,F841,RST2,RST301",
            "--exclude",
            "logs/*,data/*,outputs/*,*.log",
          ]

  # yaml formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types: [yaml]
        exclude: "environment.yaml|.pre-commit-config*.yaml"

  # jupyter notebook cell output clearing
  - repo: https://github.com/kynan/nbstripout
    rev: 0.7.1
    hooks:
      - id: nbstripout

  # jupyter notebook linting
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.9.1
    hooks:
      - id: nbqa-black
        args: ["--line-length=99"]
      - id: nbqa-isort
        args: ["--profile=black"]
      - id: nbqa-flake8
        args:
          [
            "--extend-ignore=E203,E402,E501,F401,F841",
            "--exclude=logs/*,data/*,outputs/*,*.log",
          ]
