defaults:
  - model_checkpoint
  - early_stopping
  - model_summary
  - custom_rich_progress_bar # custom_rich_progress_bar | custom_tqdm_progress_bar | rich_progress_bar
  - _self_

model_checkpoint:
  dirpath: ${paths.output_dir}/checkpoints
  filename: "epoch_{epoch:03d}"
  monitor: "val/MultiClassAccuracy"
  mode: "max"
  save_last: True
  auto_insert_metric_name: False

early_stopping:
  monitor: "val/MultiClassAccuracy"
  patience: 100
  mode: "max"

model_summary:
  max_depth: -1
