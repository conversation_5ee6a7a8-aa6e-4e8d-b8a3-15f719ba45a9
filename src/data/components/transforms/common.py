# pylint: disable=not-callable
"""TorchAudio-Compatible Audio Augmentation Module.

This module provides a set of audio augmentation transformations for PyTorch and torchaudio
pipelines. All transforms operate on torch.Tensor objects with shape [channels, samples]. They
cover common audio processing tasks such as converting to mono, normalization, time shifting, pitch
shifting, time stretching, adding Gaussian noise, applying random gain, and simulating reverb.

Audio file input and output are handled with torchaudio, making this module suitable for
integration into modern PyTorch deep learning and data loading workflows.
"""

import random
from typing import Dict, Tuple

import torch
import torchaudio


class ToMono:
    """Converts multi-channel audio to mono by averaging all channels.

    Args:
        audio (torch.Tensor): Audio tensor of shape [C, T]
    Returns:
        torch.Tensor: Mono audio of shape [1, T]
    """

    def __call__(self, audio: torch.Tensor) -> torch.Tensor:
        return audio.mean(dim=0, keepdim=True)


class Normalize:
    """Normalizes the audio to have a maximum absolute value of 1.

    Args:
        audio (torch.Tensor): Audio tensor of shape [C, T]
    Returns:
        torch.Tensor: Normalized audio tensor
    """

    def __call__(self, audio: torch.Tensor) -> torch.Tensor:
        max_value = audio.abs().max()
        return audio / max(max_value, torch.tensor(1e-8, device=audio.device))


class TimeShift:
    """Randomly shift audio left/right by up to shift seconds.

    Args:
        sr (float): Sample rate
        shift (Tuple[float, float]): Range of shift in seconds (left, right)
    """

    def __init__(self, sr: float, shift: tuple[float, float] = (-0.2, 0.2)):
        self.sr = sr
        self.left_shift, self.right_shift = shift

    def __call__(self, audio: torch.Tensor) -> torch.Tensor:
        _, num_samples = audio.shape
        shift_time = random.uniform(self.left_shift, self.right_shift)
        shift_samples = int(round(self.sr * shift_time))
        shifted_audio = torch.zeros_like(audio)
        if shift_samples == 0:
            shifted_audio = audio
        elif shift_samples < 0:
            shifted_audio[:, : num_samples + shift_samples] = audio[:, -shift_samples:]
        else:
            shifted_audio[:, shift_samples:] = audio[:, : num_samples - shift_samples]
        return shifted_audio


class PitchShift:
    """Pitch shift using torchaudio.transforms.PitchShift.

    Args:
        n_steps (int): Number of semitones to shift
        sr (int): Sample rate
    """

    def __init__(self, n_steps: int, sr: int):
        self.transform = torchaudio.transforms.PitchShift(sr, n_steps=n_steps)

    def __call__(self, audio: torch.Tensor) -> torch.Tensor:
        return self.transform(audio)


class TimeStretch:
    """Time-stretch using torchaudio (works on spectrogram).

    Args:
        rate (float): Stretching factor
        n_fft (int): FFT window size
        hop_length (int): Hop length
    """

    def __init__(self, rate: float, n_fft: int = 2048, hop_length: int = 512):
        self.rate = rate
        self.n_fft = n_fft
        self.hop_length = hop_length
        self.stretch = torchaudio.transforms.TimeStretch(
            hop_length=hop_length, n_freq=(n_fft // 2 + 1), fixed_rate=rate
        )

    def __call__(self, audio: torch.Tensor) -> torch.Tensor:
        stft = torch.stft(audio, n_fft=self.n_fft, hop_length=self.hop_length, return_complex=True)
        stft_stretch = self.stretch(stft)
        length = int(audio.shape[-1] / self.rate)
        stretched = torch.istft(
            stft_stretch, n_fft=self.n_fft, hop_length=self.hop_length, length=length
        )
        return stretched


class AddGaussianNoise:
    """Adds Gaussian noise to the audio.

    Args:
        noise_level (float): Standard deviation of noise
    """

    def __init__(self, noise_level=0.005):
        self.noise_level = noise_level

    def __call__(self, audio: torch.Tensor) -> torch.Tensor:
        noise = torch.randn_like(audio) * self.noise_level
        return audio + noise


class RandomGain:
    """Applies random gain to the audio.

    Args:
        min_gain (float): Minimum gain factor
        max_gain (float): Maximum gain factor
    """

    def __init__(self, min_gain=0.5, max_gain=1.5):
        self.min_gain = min_gain
        self.max_gain = max_gain

    def __call__(self, audio: torch.Tensor) -> torch.Tensor:
        gain = random.uniform(self.min_gain, self.max_gain)
        return audio * gain


class RandomImpulseReverb:
    """Applies random impulse reverb to the audio using torch.

    Args:
        rt60 (float): Reverb time (seconds)
        sr (int): Sample rate
        mix_ratio (float): Wet/dry mix ratio
    """

    def __init__(self, rt60=0.8, sr=22050, mix_ratio=0.5):
        self.rt60 = rt60
        self.sr = sr
        self.mix_ratio = mix_ratio

    def __call__(self, audio: torch.Tensor) -> torch.Tensor:
        device = audio.device
        reverb_len = int(self.rt60 * self.sr)
        decay_constant = -torch.log(torch.tensor(0.001)) / (self.rt60 * self.sr)
        time_points = torch.arange(reverb_len, device=device) / self.sr
        decay = torch.exp(-decay_constant * time_points)
        noise = torch.randn(reverb_len, device=device)
        impulse_response = noise * decay
        impulse_response = impulse_response / (impulse_response.norm() + 1e-8)
        reverb_audio = torch.stack(
            [
                torch.nn.functional.conv1d(
                    channel.unsqueeze(0).unsqueeze(0),
                    impulse_response.unsqueeze(0).unsqueeze(0),
                    padding=reverb_len // 2,
                ).squeeze()
                for channel in audio
            ]
        )
        mixed_audio = (1 - self.mix_ratio) * audio + self.mix_ratio * reverb_audio[
            ..., : audio.shape[-1]
        ]
        return mixed_audio


def test_transformations(audio_path: str):
    """Load audio with torchaudio and apply a sequence of augmentations, saving each result to a
    separate output file.

    Args:
        audio_path (str): Path to the input audio file.
    """
    audio, sr = torchaudio.load(audio_path)  # [channels, samples]
    if audio.dim() == 1:
        audio = audio.unsqueeze(0)
    augmentations = [
        ToMono(),
        Normalize(),
        PitchShift(n_steps=2, sr=sr),
        TimeStretch(rate=0.5),
        AddGaussianNoise(noise_level=0.005),
        RandomGain(min_gain=0.5, max_gain=1.5),
        RandomImpulseReverb(rt60=0.1, sr=sr, mix_ratio=0.9),
    ]
    for aug in augmentations:
        out_audio = aug(audio)
        output_filename = f"augmented_{aug.__class__.__name__}.wav"
        torchaudio.save(output_filename, out_audio, sr)


if __name__ == "__main__":
    test_transformations("test.m4a")
